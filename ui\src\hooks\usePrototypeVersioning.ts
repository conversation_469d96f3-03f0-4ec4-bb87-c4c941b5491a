/**
 * Hook for automatic prototype versioning integration
 * Monitors editor state and creates versions based on specific rules
 */

import { useEffect, useRef, useCallback } from 'react';
import { prototypeVersioningService } from '../services/prototypeVersioningService';

interface UsePrototypeVersioningProps {
  prototypeId: number | null;
  htmlContent: string | null;
  isGenerating: boolean;
  currentPageId: string | null;
  pages: any[];
  isVersionSwitching?: boolean;
  onVersionCreated?: (versionId: number) => void;
}

interface VersioningContext {
  lastHtmlContent: string | null;
  lastPageId: string | null;
  lastPageCount: number;
  hasInitialVersion: boolean;
  isFirstContentSet: boolean;
  isVersionSwitching: boolean;
  lastVersionSwitchTime: number;
}

export function usePrototypeVersioning({
  prototypeId,
  htmlContent,
  isGenerating,
  currentPageId,
  pages,
  isVersionSwitching = false,
  onVersionCreated
}: UsePrototypeVersioningProps) {
  const contextRef = useRef<VersioningContext>({
    lastHtmlContent: null,
    lastPageId: null,
    lastPageCount: 0,
    hasInitialVersion: false,
    isFirstContentSet: false,
    isVersionSwitching: false,
    lastVersionSwitchTime: 0
  });

  const lastGenerationStateRef = useRef<boolean>(false);

  /**
   * Handle version creation completion
   */
  const handleVersionCreated = useCallback((versionId: number) => {
    console.log('🎉 Version created callback:', versionId);
    onVersionCreated?.(versionId);
  }, [onVersionCreated]);

  /**
   * Check if this is the first page being created
   */
  const isFirstPage = useCallback((): boolean => {
    const context = contextRef.current;
    // Only consider it first page if:
    // 1. We have exactly 1 page
    // 2. We haven't created an initial version yet
    // 3. We have HTML content (page has been generated)
    // 4. This is the first time we're setting content (not a subsequent update)
    return pages.length === 1 &&
           !context.hasInitialVersion &&
           htmlContent &&
           !context.isFirstContentSet;
  }, [pages.length, htmlContent]);

  /**
   * Check if this is a content modification (not navigation)
   */
  const isContentModification = useCallback((): boolean => {
    const context = contextRef.current;

    // CRITICAL: Don't consider it a modification if this is the first page being created
    if (pages.length === 1 && !context.hasInitialVersion) {
      console.log('⏭️ Not a modification: First page creation');
      return false;
    }

    // CRITICAL: Don't consider it a modification if we're adding a new page
    if (pages.length > context.lastPageCount) {
      console.log('⏭️ Not a modification: New page addition detected');
      return false;
    }

    // CRITICAL: Don't consider it a modification if we're switching versions
    if (isVersionSwitching || context.isVersionSwitching) {
      console.log('⏭️ Not a modification: Version switching in progress');
      return false;
    }

    // Don't consider it a modification if version switch happened recently (within 2 seconds)
    const timeSinceLastSwitch = Date.now() - context.lastVersionSwitchTime;
    if (timeSinceLastSwitch < 2000) {
      console.log('⏭️ Not a modification: Recent version switch detected');
      return false;
    }

    // Check for actual content modifications on the same page
    const samePageContentChanged = context.lastPageId === currentPageId &&
                                   context.lastHtmlContent !== htmlContent &&
                                   htmlContent &&
                                   context.lastHtmlContent &&
                                   context.hasInitialVersion; // Only after initial version exists

    // Check for manual edits (not during generation)
    const manualEdit = !isGenerating &&
                       context.lastHtmlContent !== htmlContent &&
                       htmlContent &&
                       context.lastHtmlContent &&
                       context.hasInitialVersion &&
                       context.lastPageId === currentPageId; // Same page only

    const isModification = samePageContentChanged || manualEdit;

    if (isModification) {
      console.log('✏️ Content modification detected:', {
        samePageContentChanged,
        manualEdit,
        currentPageId,
        lastPageId: context.lastPageId,
        contentLengthChange: (htmlContent?.length || 0) - (context.lastHtmlContent?.length || 0)
      });
    }

    return isModification;
  }, [currentPageId, htmlContent, isGenerating, pages.length, isVersionSwitching]);

  /**
   * Check if this is just page navigation
   */
  const isPageNavigation = useCallback((): boolean => {
    const context = contextRef.current;
    
    // Page changed but content is the same or we're just loading existing content
    return context.lastPageId !== currentPageId && context.lastHtmlContent === htmlContent;
  }, [currentPageId, htmlContent]);

  /**
   * Check if this is a new page addition
   */
  const isNewPageAddition = useCallback((): boolean => {
    const context = contextRef.current;

    // New page addition if:
    // 1. Page count increased from last check
    // 2. We have more than 1 page total (not the first page)
    // 3. We already have an initial version (prototype exists)
    const pageCountIncreased = pages.length > context.lastPageCount;
    const hasMultiplePages = pages.length > 1;
    const hasExistingVersion = context.hasInitialVersion;

    const isNewPage = pageCountIncreased && hasMultiplePages && hasExistingVersion;

    if (isNewPage) {
      console.log('🆕 New page addition detected:', {
        currentPageCount: pages.length,
        lastPageCount: context.lastPageCount,
        hasInitialVersion: context.hasInitialVersion,
        currentPageId
      });
    }

    return isNewPage;
  }, [pages.length, currentPageId]);

  /**
   * Create initial version for new prototype
   */
  const createInitialVersion = useCallback(async () => {
    if (!prototypeId || !htmlContent) {
      return;
    }

    console.log('🎯 Creating initial version for new prototype (readdy.ai style)');

    try {
      const versionId = await prototypeVersioningService.createInitialVersion(
        prototypeId,
        htmlContent
      );

      if (versionId) {
        // hasInitialVersion is already set synchronously in the main effect
        handleVersionCreated(versionId);
        console.log('✅ Initial version V1 created successfully - version controls will now appear');

        // Emit event to trigger version UI to appear
        window.dispatchEvent(new CustomEvent('firstVersionCreated', {
          detail: { prototypeId, versionId }
        }));
      } else {
        // If version creation failed, reset the flag
        contextRef.current.hasInitialVersion = false;
      }
    } catch (error) {
      console.error('❌ Failed to create initial version:', error);
      // If version creation failed, reset the flag
      contextRef.current.hasInitialVersion = false;
    }
  }, [prototypeId, htmlContent, handleVersionCreated]);

  /**
   * Create version for content modifications
   */
  const createModificationVersion = useCallback(async (immediate = false) => {
    if (!prototypeId || !htmlContent) {
      return;
    }

    const operationType = lastGenerationStateRef.current ? 'generation' : 'manual_edit';
    const changeDescription = lastGenerationStateRef.current 
      ? 'AI-generated content update'
      : 'Manual content modification';

    console.log(`🎯 Creating ${operationType} version for content modification`);

    try {
      await prototypeVersioningService.createContentModificationVersion(
        prototypeId,
        htmlContent,
        operationType,
        {
          changeDescription,
          immediate
        }
      );
    } catch (error) {
      console.error('❌ Failed to create modification version:', error);
    }
  }, [prototypeId, htmlContent]);

  /**
   * Main versioning logic
   */
  useEffect(() => {
    if (!prototypeId) {
      return;
    }

    // Don't run versioning logic while generating content
    if (isGenerating) {
      return;
    }

    // Add a small delay to ensure the editor state has stabilized
    const timer = setTimeout(() => {
      const context = contextRef.current;

      // Additional safety checks
      if (!htmlContent || htmlContent.trim().length === 0) {
        console.log('⏭️ Skipping versioning: No HTML content');
        return;
      }

      // Don't run if we're in the middle of a page transition
      if (context.lastPageId !== currentPageId && !htmlContent) {
        console.log('⏭️ Skipping versioning: Page transition without content');
        return;
      }
    
    // Determine versioning context
    const versioningContext = {
      isFirstPage: isFirstPage(),
      isContentModification: isContentModification(),
      isPageNavigation: isPageNavigation(),
      isNewPageAddition: isNewPageAddition(),
      isUIStateChange: false // We don't track UI state changes in this hook
    };

    console.log('🔍 Versioning context:', {
      ...versioningContext,
      prototypeId,
      currentPageId,
      lastPageId: context.lastPageId,
      htmlContentLength: htmlContent?.length || 0,
      lastHtmlContentLength: context.lastHtmlContent?.length || 0,
      pagesCount: pages.length,
      lastPageCount: context.lastPageCount,
      isGenerating,
      isVersionSwitching,
      hasInitialVersion: context.hasInitialVersion,
      isFirstContentSet: context.isFirstContentSet
    });

    // CRITICAL: Rule priority order matters - check exclusions first!

    // Rule 1: Create initial version for first page
    if (versioningContext.isFirstPage && htmlContent && !context.isFirstContentSet) {
      console.log('📝 Rule 1: Creating initial version for first page');
      // Set flags immediately to prevent duplicate version creation
      context.isFirstContentSet = true;
      context.hasInitialVersion = true; // Set immediately to prevent race condition
      createInitialVersion();
    }

    // Rule 2: PRIORITY - Skip new page additions (ALWAYS skip, regardless of other conditions)
    else if (versioningContext.isNewPageAddition) {
      console.log('⏭️ Rule 2: Skipping version creation for new page addition (PRIORITY RULE)');
    }

    // Rule 3: Skip page navigation
    else if (versioningContext.isPageNavigation) {
      console.log('⏭️ Rule 3: Skipping version creation for page navigation');
    }

    // Rule 4: Skip version switching
    else if (isVersionSwitching || context.isVersionSwitching) {
      console.log('⏭️ Rule 4: Skipping version creation for version switching');
    }

    // Rule 5: Create version for content modifications (only if no exclusions apply)
    else if (versioningContext.isContentModification && context.hasInitialVersion) {
      console.log('📝 Rule 5: Creating version for content modification');
      createModificationVersion();
    }

    // Rule 6: Skip if no action needed
    else {
      console.log('⏭️ Rule 6: No versioning action needed', {
        isFirstPage: versioningContext.isFirstPage,
        isContentModification: versioningContext.isContentModification,
        isNewPageAddition: versioningContext.isNewPageAddition,
        isPageNavigation: versioningContext.isPageNavigation,
        isVersionSwitching: isVersionSwitching || context.isVersionSwitching,
        hasInitialVersion: context.hasInitialVersion,
        isFirstContentSet: context.isFirstContentSet
      });
    }

      // Update context for next iteration
      context.lastHtmlContent = htmlContent;
      context.lastPageId = currentPageId;
      context.lastPageCount = pages.length;

      // Track version switching state
      if (isVersionSwitching) {
        context.isVersionSwitching = true;
        context.lastVersionSwitchTime = Date.now();
        console.log('🔄 Version switching detected, marking context');
      } else if (context.isVersionSwitching) {
        // Clear version switching flag after a delay
        setTimeout(() => {
          context.isVersionSwitching = false;
          console.log('✅ Version switching flag cleared');
        }, 1000);
      }
    }, 100); // 100ms delay to let editor state stabilize

    return () => clearTimeout(timer);
  }, [
    prototypeId,
    htmlContent,
    currentPageId,
    pages.length,
    isGenerating,
    isVersionSwitching,
    isFirstPage,
    isContentModification,
    isPageNavigation,
    isNewPageAddition,
    createInitialVersion,
    createModificationVersion
  ]);

  /**
   * Track generation state changes for operation type detection
   */
  useEffect(() => {
    // When generation completes, check if we should create a version
    if (lastGenerationStateRef.current && !isGenerating && htmlContent) {
      const context = contextRef.current;

      // Don't create version if this was a new page generation
      if (pages.length > context.lastPageCount) {
        console.log('🤖 Generation completed for new page - NOT creating version (following rules)');
      } else {
        console.log('🤖 Generation completed for existing page - creating version immediately');
        createModificationVersion(true); // immediate = true
      }
    }

    lastGenerationStateRef.current = isGenerating;
  }, [isGenerating, htmlContent, pages.length, createModificationVersion]);

  /**
   * Listen for version creation events
   */
  useEffect(() => {
    const handleVersionCreatedEvent = (event: CustomEvent) => {
      const { prototypeId: eventPrototypeId, versionId } = event.detail;
      if (eventPrototypeId === prototypeId) {
        handleVersionCreated(versionId);
      }
    };

    window.addEventListener('prototypeVersionCreated', handleVersionCreatedEvent as EventListener);
    
    return () => {
      window.removeEventListener('prototypeVersionCreated', handleVersionCreatedEvent as EventListener);
    };
  }, [prototypeId, handleVersionCreated]);

  /**
   * Cleanup on unmount or prototype change
   */
  useEffect(() => {
    return () => {
      if (prototypeId) {
        prototypeVersioningService.cleanup(prototypeId);
      }
    };
  }, [prototypeId]);

  /**
   * Reset context when prototype changes
   */
  useEffect(() => {
    // Only reset context when prototype ID actually changes (not on initial mount)
    const context = contextRef.current;
    const currentPrototypeId = prototypeId;

    // If this is a new prototype ID, reset the context
    if (currentPrototypeId && context.lastPageCount > 0) {
      console.log('🔄 Prototype changed, resetting versioning context');
      contextRef.current = {
        lastHtmlContent: null,
        lastPageId: null,
        lastPageCount: 0,
        hasInitialVersion: false,
        isFirstContentSet: false,
        isVersionSwitching: false,
        lastVersionSwitchTime: 0
      };
    }
  }, [prototypeId]);

  return {
    isVersionCreationInProgress: prototypeId ? prototypeVersioningService.isVersionCreationInProgress(prototypeId) : false,
    cancelPendingVersionCreation: () => {
      if (prototypeId) {
        prototypeVersioningService.cancelPendingVersionCreation(prototypeId);
      }
    }
  };
}

export default usePrototypeVersioning;
